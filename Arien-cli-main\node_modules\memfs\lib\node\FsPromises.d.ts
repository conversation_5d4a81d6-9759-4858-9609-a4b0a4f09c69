import type * as opts from './types/options';
import type * as misc from './types/misc';
import type { FsCallbackApi, FsPromisesApi } from './types';
export declare class FsPromises implements FsPromisesApi {
    protected readonly fs: FsCallbackApi;
    readonly FileHandle: new (...args: unknown[]) => misc.IFileHandle;
    readonly constants: {
        O_RDONLY: number;
        O_WRONLY: number;
        O_RDWR: number;
        S_IFMT: number;
        S_IFREG: number;
        S_IFDIR: number;
        S_IFCHR: number;
        S_IFBLK: number;
        S_IFIFO: number;
        S_IFLNK: number;
        S_IFSOCK: number;
        O_CREAT: number;
        O_EXCL: number;
        O_NOCTTY: number;
        O_TRUNC: number;
        O_APPEND: number;
        O_DIRECTORY: number;
        O_NOATIME: number;
        O_NOFOLLOW: number;
        O_SYNC: number;
        O_SYMLINK: number;
        O_DIRECT: number;
        O_NONBLOCK: number;
        S_IRWXU: number;
        S_IRUSR: number;
        S_IWUSR: number;
        S_IXUSR: number;
        S_IRWXG: number;
        S_IRGRP: number;
        S_IWGRP: number;
        S_IXGRP: number;
        S_IRWXO: number;
        S_IROTH: number;
        S_IWOTH: number;
        S_IXOTH: number;
        F_OK: number;
        R_OK: number;
        W_OK: number;
        X_OK: number;
        UV_FS_SYMLINK_DIR: number;
        UV_FS_SYMLINK_JUNCTION: number;
        UV_FS_COPYFILE_EXCL: number;
        UV_FS_COPYFILE_FICLONE: number;
        UV_FS_COPYFILE_FICLONE_FORCE: number;
        COPYFILE_EXCL: number;
        COPYFILE_FICLONE: number;
        COPYFILE_FICLONE_FORCE: number;
    };
    constructor(fs: FsCallbackApi, FileHandle: new (...args: unknown[]) => misc.IFileHandle);
    readonly cp: (...args: any[]) => Promise<any>;
    readonly opendir: (...args: any[]) => Promise<any>;
    readonly statfs: (...args: any[]) => Promise<any>;
    readonly lutimes: (...args: any[]) => Promise<any>;
    readonly access: (...args: any[]) => Promise<any>;
    readonly chmod: (...args: any[]) => Promise<any>;
    readonly chown: (...args: any[]) => Promise<any>;
    readonly copyFile: (...args: any[]) => Promise<any>;
    readonly lchmod: (...args: any[]) => Promise<any>;
    readonly lchown: (...args: any[]) => Promise<any>;
    readonly link: (...args: any[]) => Promise<any>;
    readonly lstat: (...args: any[]) => Promise<any>;
    readonly mkdir: (...args: any[]) => Promise<any>;
    readonly mkdtemp: (...args: any[]) => Promise<any>;
    readonly readdir: (...args: any[]) => Promise<any>;
    readonly readlink: (...args: any[]) => Promise<any>;
    readonly realpath: (...args: any[]) => Promise<any>;
    readonly rename: (...args: any[]) => Promise<any>;
    readonly rmdir: (...args: any[]) => Promise<any>;
    readonly rm: (...args: any[]) => Promise<any>;
    readonly stat: (...args: any[]) => Promise<any>;
    readonly symlink: (...args: any[]) => Promise<any>;
    readonly truncate: (...args: any[]) => Promise<any>;
    readonly unlink: (...args: any[]) => Promise<any>;
    readonly utimes: (...args: any[]) => Promise<any>;
    readonly readFile: (id: misc.TFileHandle, options?: opts.IReadFileOptions | string) => Promise<misc.TDataOut>;
    readonly appendFile: (path: misc.TFileHandle, data: misc.TData, options?: opts.IAppendFileOptions | string) => Promise<void>;
    readonly open: (path: misc.PathLike, flags?: misc.TFlags, mode?: misc.TMode) => Promise<any>;
    readonly writeFile: (id: misc.TFileHandle, data: misc.TPromisesData, options?: opts.IWriteFileOptions) => Promise<void>;
    readonly watch: () => never;
}
