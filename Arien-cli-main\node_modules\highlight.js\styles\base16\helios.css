pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Helios
  Author: <PERSON> (https://github.com/reyemxela)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme helios
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1d2021  Default Background
base01  #383c3e  Lighter Background (Used for status bars, line number and folding marks)
base02  #53585b  Selection Background
base03  #6f7579  Comments, Invisibles, Line Highlighting
base04  #cdcdcd  Dark Foreground (Used for status bars)
base05  #d5d5d5  Default Foreground, Caret, Delimiters, Operators
base06  #dddddd  Light Foreground (Not often used)
base07  #e5e5e5  Light Background (Not often used)
base08  #d72638  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #eb8413  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f19d1a  Classes, Markup Bold, Search Text Background
base0B  #88b92d  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #1ba595  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #1e8bac  Functions, Methods, Attribute IDs, Headings
base0E  #be4264  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #c85e0d  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #d5d5d5;
  background: #1d2021
}
.hljs::selection,
.hljs ::selection {
  background-color: #53585b;
  color: #d5d5d5
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #6f7579 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #6f7579
}
/* base04 - #cdcdcd -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #cdcdcd
}
/* base05 - #d5d5d5 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d5d5d5
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d72638
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #eb8413
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f19d1a
}
.hljs-strong {
  font-weight: bold;
  color: #f19d1a
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #88b92d
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #1ba595
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #1e8bac
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #be4264
}
.hljs-emphasis {
  color: #be4264;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #c85e0d
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}