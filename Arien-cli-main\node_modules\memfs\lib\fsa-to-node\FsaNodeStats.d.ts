import type * as misc from '../node/types/misc';
export declare class FsaNodeStats<T = misc.TStatNumber> implements misc.IStats<T> {
    protected readonly kind: 'file' | 'directory';
    readonly uid: T;
    readonly gid: T;
    readonly rdev: T;
    readonly blksize: T;
    readonly ino: T;
    readonly size: T;
    readonly blocks: T;
    readonly atime: Date;
    readonly mtime: Date;
    readonly ctime: Date;
    readonly birthtime: Date;
    readonly atimeMs: T;
    readonly mtimeMs: T;
    readonly ctimeMs: T;
    readonly birthtimeMs: T;
    readonly dev: T;
    readonly mode: T;
    readonly nlink: T;
    constructor(isBigInt: boolean, size: T, kind: 'file' | 'directory');
    isDirectory(): boolean;
    isFile(): boolean;
    isBlockDevice(): boolean;
    isCharacterDevice(): boolean;
    isSymbolicLink(): boolean;
    isFIFO(): boolean;
    isSocket(): boolean;
}
