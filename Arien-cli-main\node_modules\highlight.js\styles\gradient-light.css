pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

Gradient Light (c) <PERSON><PERSON> <<EMAIL>>

*/
.hljs {
  background-color: #f9ccff;
  background-image: linear-gradient(295deg, #f9ccff 0%, #e6bbf9 11%, #9ec6f9 32%, #55e6ee 60%, #91f5d1 74%, #f9ffbf 98%);
  color: #250482
}
.hljs-subtr {
  color: #01958B
}
.hljs-doctag,
.hljs-meta,
.hljs-comment,
.hljs-quote {
  color: #CB7200
}
.hljs-selector-tag,
.hljs-selector-id,
.hljs-template-tag,
.hljs-regexp,
.hljs-attr,
.hljs-tag {
  color: #07BD5F
}
.hljs-params,
.hljs-selector-class,
.hljs-bullet {
  color: #43449F
}
.hljs-keyword,
.hljs-section,
.hljs-meta .hljs-keyword,
.hljs-symbol,
.hljs-type {
  color: #7D2801
}
.hljs-addition,
.hljs-number,
.hljs-link {
  color: #7F0096
}
.hljs-string {
  color: #2681ab
}
.hljs-attribute,
.hljs-addition {
  color: #296562
}
.hljs-variable,
.hljs-template-variable {
  color: #025C8F
}
.hljs-built_in,
.hljs-formula,
.hljs-name,
.hljs-title,
.hljs-class,
.hljs-function {
  color: #529117
}
.hljs-selector-pseudo,
.hljs-deletion,
.hljs-literal {
  color: #AD13FF
}
.hljs-emphasis,
.hljs-quote {
  font-style: italic
}
.hljs-params,
.hljs-selector-class,
.hljs-strong,
.hljs-selector-tag,
.hljs-selector-id,
.hljs-template-tag,
.hljs-section,
.hljs-keyword {
  font-weight: bold
}