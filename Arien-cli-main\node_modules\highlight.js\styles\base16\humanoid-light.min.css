/*!
  Theme: Humanoid light
  Author: <PERSON> (<PERSON><PERSON>) Friese
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#232629;background:#f8f8f2}.hljs ::selection,.hljs::selection{background-color:#deded8;color:#232629}.hljs-comment{color:#c0c0bd}.hljs-tag{color:#60615d}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#232629}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#b0151a}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#ff3d00}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ffb627}.hljs-strong{font-weight:700;color:#ffb627}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#388e3c}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#008e8e}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#0082c9}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#700f98}.hljs-emphasis{color:#700f98;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b27701}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}